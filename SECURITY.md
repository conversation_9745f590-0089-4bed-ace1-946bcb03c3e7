# Security Policy

## Supported Versions

We actively support the following versions of NexaDash:

| Version | Supported          |
| ------- | ------------------ |
| 1.x.x   | :white_check_mark: |
| < 1.0   | :x:                |

## Reporting a Vulnerability

We take the security of NexaDash seriously. If you believe you have found a security vulnerability, please report it to us as described below.

### How to Report

**Please do NOT report security vulnerabilities through public GitHub issues.**

Instead, please report them via email to: [<EMAIL>](mailto:<EMAIL>)

### What to Include

Please include the following information in your report:

- A description of the vulnerability
- Steps to reproduce the issue
- Possible impact of the vulnerability
- Any suggested fixes or mitigation steps

### Response Timeline

- We will acknowledge receipt of your vulnerability report within 48 hours
- We will provide a detailed response within 5 business days
- We will work with you to understand and resolve the issue
- We will notify you when the vulnerability has been fixed

## Security Best Practices

When using NexaDash in production:

1. **Environment Variables**: Never commit sensitive environment variables
2. **Dependencies**: Keep all dependencies up to date
3. **HTTPS**: Always use HTTPS in production
4. **Headers**: Implement proper security headers (included in next.config.mjs)
5. **Authentication**: Implement proper authentication and authorization
6. **Input Validation**: Always validate user inputs
7. **CORS**: Configure CORS properly for your use case

## Security Features

NexaDash includes several security features by default:

- **Security Headers**: X-Frame-Options, X-Content-Type-Options, etc.
- **Content Security Policy**: Basic CSP for SVG handling
- **TypeScript**: Type safety to prevent common errors
- **ESLint Rules**: Security-focused linting rules
- **Dependencies**: Regularly updated dependencies

## Disclosure Policy

When we receive a security bug report, we will:

1. Confirm the problem and determine affected versions
2. Audit code to find any similar problems
3. Prepare fixes for all supported versions
4. Release new versions as soon as possible
5. Publicly disclose the issue after fixes are available

Thank you for helping keep NexaDash and its users safe!
