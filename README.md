# NexaDash - Production-Ready Next.js Admin Dashboard

A modern, production-ready admin dashboard built with Next.js 14, TypeScript, Tailwind CSS, and shadcn/ui components.

## ✨ Features

- 🚀 **Next.js 14** with App Router
- 🎨 **Tailwind CSS** for styling
- 🧩 **shadcn/ui** component library
- 📊 **Recharts** for data visualization
- 🔒 **TypeScript** for type safety
- 🎯 **ESLint & Prettier** for code quality
- 🐶 **Husky** for git hooks (pre-configured)
- 📱 **Responsive** design
- 🌙 **Dark mode** support
- ♿ **Accessibility** focused

## 🛠️ Tech Stack

- **Framework:** Next.js 14
- **Language:** TypeScript
- **Styling:** Tailwind CSS
- **Components:** shadcn/ui, Radix UI
- **Charts:** Recharts
- **Icons:** Lucide React
- **Code Quality:** ESLint, Prettier
- **Git Hooks:** <PERSON>sky, lint-staged

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (>= 18.17.0)
- **pnpm** (>= 8.0.0) - recommended package manager
- **Git**

## 🚀 Getting Started

### 1. Clone the repository

```bash
git clone <repository-url>
cd nexadash-next-free-main
```

### 2. Install dependencies

```bash
pnpm install
```

### 3. Set up environment variables

```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration values.

### 4. Run the development server

```bash
pnpm dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

## 📜 Available Scripts

```bash
# Development
pnpm dev              # Start development server
pnpm build            # Build for production
pnpm start            # Start production server

# Code Quality
pnpm lint             # Run ESLint
pnpm lint:fix         # Fix ESLint issues
pnpm format           # Format code with Prettier
pnpm format:check     # Check code formatting
pnpm type-check       # Run TypeScript compiler

# Git Hooks
pnpm pre-commit       # Run lint-staged (used by Husky)

# Analysis
pnpm analyze          # Analyze bundle size
pnpm clean            # Clean build artifacts
```

## 🔧 Configuration

### ESLint

The project uses a comprehensive ESLint configuration with:

- Next.js recommended rules
- TypeScript support
- React hooks rules
- Import organization
- Tailwind CSS class ordering
- Accessibility checks

### Prettier

Configured with:

- Single quotes
- No semicolons
- 2-space indentation
- Tailwind CSS class sorting

### Husky (Git Hooks)

Pre-configured but commented out. To enable:

1. Uncomment the hooks in `.husky/pre-commit` and `.husky/commit-msg`
2. Run `pnpm prepare` to install hooks

## 📁 Project Structure

```
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authenticated routes
│   ├── (noauth)/          # Public routes
│   ├── globals.css        # Global styles
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   ├── custom/           # Custom components
│   ├── icons/            # Icon components
│   └── layout/           # Layout components
├── lib/                  # Utilities and configurations
├── public/               # Static assets
├── .vscode/              # VS Code settings
├── .husky/               # Git hooks
└── config files          # Various config files
```

## 🎨 Styling

This project uses:

- **Tailwind CSS** for utility-first styling
- **CSS Variables** for theming
- **shadcn/ui** for consistent component design
- **Responsive design** principles

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Configure environment variables
3. Deploy automatically on push

### Other Platforms

The project is configured for standalone output and can be deployed to:

- Docker containers
- Traditional hosting
- Edge platforms

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Code Quality Standards

- Follow existing code patterns
- Write TypeScript with proper types
- Add appropriate comments
- Test your changes
- Run `pnpm lint` and `pnpm format` before committing

## 📝 Environment Variables

See `.env.example` for all available configuration options.

## 🐛 Troubleshooting

### Common Issues

**Build fails with TypeScript errors:**

```bash
pnpm type-check
```

**Formatting issues:**

```bash
pnpm format
```

**Linting errors:**

```bash
pnpm lint:fix
```

## 📚 Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [shadcn/ui](https://ui.shadcn.com)
- [TypeScript](https://www.typescriptlang.org/docs)

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

Made with ❤️ by [DesignByte](https://designbyte.io)
