# Contributing to NexaDash

Thank you for considering contributing to NexaDash! This document provides guidelines and information for contributors.

## 🚀 Getting Started

1. Fork the repository
2. Clone your fork: `git clone <your-fork-url>`
3. Install dependencies: `pnpm install`
4. Create a branch: `git checkout -b feature/your-feature-name`

## 📋 Development Guidelines

### Code Standards

- **TypeScript**: Use proper types, avoid `any`
- **Components**: Follow the existing component patterns
- **Naming**: Use descriptive names for variables and functions
- **Comments**: Add JSDoc comments for complex functions
- **Testing**: Write tests for new features (when test framework is added)

### Commit Convention

We follow the [Conventional Commits](https://conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### Types:

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools

#### Examples:

```
feat: add dark mode toggle to header
fix: resolve layout shift on mobile devices
docs: update installation instructions
style: format code with prettier
refactor: extract common utility functions
```

## 🔧 Development Workflow

### Before Starting Work

1. Check existing issues and pull requests
2. Create or comment on an issue to discuss your planned changes
3. Wait for maintainer approval for significant changes

### During Development

1. Follow the existing code style and patterns
2. Run `pnpm lint` to check for linting errors
3. Run `pnpm format` to format your code
4. Run `pnpm type-check` to check TypeScript types
5. Test your changes thoroughly

### Before Submitting

1. Ensure all checks pass: `pnpm lint && pnpm type-check`
2. Update documentation if needed
3. Add or update tests if applicable
4. Write a clear commit message

## 📝 Pull Request Process

1. **Create a descriptive PR title** following conventional commits
2. **Fill out the PR template** with all required information
3. **Link related issues** using keywords (fixes #123)
4. **Add screenshots** for UI changes
5. **Request review** from maintainers
6. **Address feedback** promptly and professionally

### PR Checklist

- [ ] Code follows the project's coding standards
- [ ] Self-review completed
- [ ] Documentation updated (if needed)
- [ ] Tests added/updated (if applicable)
- [ ] No console errors or warnings
- [ ] Responsive design tested
- [ ] Accessibility considerations addressed

## 🎨 UI/UX Guidelines

### Design Principles

- **Consistency**: Follow existing design patterns
- **Accessibility**: Ensure components are accessible
- **Performance**: Optimize for speed and efficiency
- **Responsive**: Design for all screen sizes

### Component Guidelines

- Use shadcn/ui components when possible
- Follow the existing component structure
- Add proper TypeScript interfaces
- Include proper accessibility attributes

## 🐛 Bug Reports

When reporting bugs, please include:

- **Clear title and description**
- **Steps to reproduce**
- **Expected vs actual behavior**
- **Screenshots or videos** (if applicable)
- **Environment information** (OS, browser, etc.)
- **Code snippets** (if relevant)

## 💡 Feature Requests

For feature requests, please include:

- **Clear description** of the feature
- **Use case** and why it's needed
- **Proposed solution** (if you have one)
- **Alternative solutions** considered
- **Mockups or designs** (if applicable)

## 🔍 Code Review Guidelines

### For Contributors

- Be open to feedback and suggestions
- Respond promptly to review comments
- Ask questions if something is unclear
- Update your PR based on feedback

### For Reviewers

- Be respectful and constructive
- Explain the reasoning behind suggestions
- Approve when changes are satisfactory
- Provide specific, actionable feedback

## 📚 Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [shadcn/ui Documentation](https://ui.shadcn.com)
- [React Documentation](https://react.dev)

## 🙋‍♀️ Questions?

If you have questions about contributing:

1. Check existing issues and discussions
2. Create a new issue with the "question" label
3. Reach out to maintainers

Thank you for contributing to NexaDash! 🎉
