# Normalize line endings to LF in the repo
* text=auto eol=lf

# Treat common binary assets as binary (no text normalization)
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.webp binary
*.svg binary
*.ico binary
*.ttf binary
*.otf binary
*.woff binary
*.woff2 binary
*.mp4 binary
*.mp3 binary

# Shell scripts should always use LF
*.sh text eol=lf

# Keep lockfiles as-is
pnpm-lock.yaml -text
package-lock.json -text
yarn.lock -text

# Ensure patch files keep CRLFs if present
*.patch -text

