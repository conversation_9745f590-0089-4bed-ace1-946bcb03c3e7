{"name": "admin-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit", "pre-commit": "lint-staged", "prepare": "husky install", "test": "echo \"No tests specified\" && exit 0", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out dist build"}, "dependencies": {"@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-table": "^8.19.3", "class-variance-authority": "^0.7.0", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.1.7", "input-otp": "^1.2.4", "lucide-react": "^0.379.0", "next": "14.2.3", "next-themes": "^0.3.0", "react": "^18", "react-countup": "^6.5.3", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-quill": "^2.0.0", "react-time-picker": "^7.0.0", "recharts": "^2.12.7", "sonner": "^1.5.0", "sortablejs": "^1.15.2", "tailwind-merge": "^2.3.0"}, "devDependencies": {"@next/bundle-analyzer": "^14.2.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.2.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.3", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-tailwindcss": "^3.17.4", "husky": "^9.0.11", "lint-staged": "^15.2.7", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "engines": {"node": ">=18.17.0", "pnpm": ">=8.0.0"}}